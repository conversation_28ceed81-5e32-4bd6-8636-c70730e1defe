using JangLegal.Services;
using Microsoft.AspNetCore.Mvc;

namespace JangLegal.Controllers;

[ApiController]
[Route("api/[controller]")]
public class FileUploadController : ControllerBase
{
    private readonly AttachmentService _attachmentService;
    private readonly ILogger<FileUploadController> _logger;

    public FileUploadController(AttachmentService attachmentService, ILogger<FileUploadController> logger)
    {
        _attachmentService = attachmentService;
        _logger = logger;
    }

    [HttpPost("upload")]
    public async Task<IActionResult> UploadFile(
        IFormFile file, 
        [FromForm] int courtCaseId, 
        [FromForm] int attachmentTypeId,
        [FromForm] string userId = "system")
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { success = false, message = "No file selected" });
            }

            var result = await _attachmentService.SaveAttachmentAsync(file, courtCaseId, attachmentTypeId, userId);
            
            if (result.Success)
            {
                return Ok(new { 
                    success = true, 
                    message = result.Message, 
                    attachment = result.Attachment 
                });
            }
            else
            {
                return BadRequest(new { success = false, message = result.Message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file for court case {CourtCaseId}", courtCaseId);
            return StatusCode(500, new { success = false, message = "An error occurred while uploading the file" });
        }
    }

    [HttpDelete("delete/{attachmentId}")]
    public async Task<IActionResult> DeleteFile(Guid attachmentId, [FromQuery] string userId = "system")
    {
        try
        {
            var result = await _attachmentService.DeleteAttachmentAsync(attachmentId, userId);
            
            if (result.Success)
            {
                return Ok(new { success = true, message = result.Message });
            }
            else
            {
                return BadRequest(new { success = false, message = result.Message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting attachment {AttachmentId}", attachmentId);
            return StatusCode(500, new { success = false, message = "An error occurred while deleting the file" });
        }
    }
}
