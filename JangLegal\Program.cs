using FluentValidation;
using JangLegal.Components;
using JangLegal.DTO;
using JangLegal.Models;
using JangLegal.Services;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.Negotiate;
using Microsoft.EntityFrameworkCore;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor;

var builder = WebApplication.CreateBuilder(args);
var connectionString = builder.Configuration.GetConnectionString("DbConnection");

Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Mzc4NDgwM0AzMjM5MmUzMDJlMzAzYjMyMzkzYkw2M0E4QXZETGFoRmtCVkNEenNUbDdkTVB0cGJCcnY2ajBwdjQyKzV0NDQ9");

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents(options =>
    {
        options.DetailedErrors = true;
    });

builder.Services.AddControllers();

builder.Services.AddSyncfusionBlazor();

// Replace DbContext registration with DbContextFactory
builder.Services.AddDbContextFactory<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString, sqlOptions =>
    {
        sqlOptions.EnableRetryOnFailure(
            maxRetryCount: 5,
            maxRetryDelay: TimeSpan.FromSeconds(30),
            errorNumbersToAdd: null);
    })
    .LogTo(Console.WriteLine, LogLevel.Information)
    .EnableSensitiveDataLogging()
    .EnableDetailedErrors()
);

// Update service registrations to use IDbContextFactory
builder.Services.AddScoped<AppAuthService>();
builder.Services.AddScoped<LawFirmDataService>();
builder.Services.AddScoped<CourtDataService>();
builder.Services.AddScoped<CaseCategoryService>();

builder.Services.AddScoped<CaseVerdictService>();
builder.Services.AddScoped<CouncilOfGroupCompanyService>();
builder.Services.AddScoped<CouncilOfNonGroupCompanyService>();
builder.Services.AddScoped<PlaintiffService>();
builder.Services.AddScoped<PlaintiffTypeService>();
builder.Services.AddScoped<CaseEmployeeService>();
builder.Services.AddScoped<CourtCaseService>();
builder.Services.AddScoped<CaseNatureService>();
builder.Services.AddScoped<AttachmentService>();


builder.Services.AddFluentUIComponents();

builder.Services.AddAuthentication(NegotiateDefaults.AuthenticationScheme)
    .AddNegotiate();
builder.Services.AddAuthorization(options =>
{
    options.FallbackPolicy = options.DefaultPolicy;
});
builder.Services.AddCascadingAuthenticationState();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseAntiforgery();
app.UseAuthentication();
app.UseAuthorization();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.MapControllers();

app.Run();
