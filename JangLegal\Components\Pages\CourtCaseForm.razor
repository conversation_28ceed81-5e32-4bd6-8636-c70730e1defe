@page "/court-cases/new"
@page "/court-cases/edit/{Id:int}"
@rendermode InteractiveServer
@inject CourtCaseService CaseService
@inject PlaintiffTypeService PlaintiffTypeService
@inject NavigationManager NavMgr


<div class="d-flex justify-content-between align-items-center mb-3">
    <h3>@(Id == null ? "New Court Case" : "Edit Court Case")</h3>
    <SfButton OnClick="@(() => NavMgr.NavigateTo("/court-cases", true))" CssClass="e-outline">Back to List</SfButton>
</div>
<SfToast @ref="ToastObj" />
<EditForm Model="CaseDto" OnValidSubmit="SaveCase" FormName="court_case_form">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="card mb-3">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <SfTextBox @bind-Value="CaseDto.CaseNumber"
                               Placeholder="Case Number"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                    <ValidationMessage For="@(() => CaseDto.CaseNumber)" />
                </div>

                <div class="col-md-8">
                    <SfTextBox @bind-Value="CaseDto.Title"
                               Placeholder="Case Title"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                </div>

                <div class="col-md-4">
                    <SfDropDownList TValue="int" TItem="CourtDto"
                                    @bind-Value="CaseDto.CourtId"
                                    DataSource="@Courts"
                                    AllowFiltering="true"
                                    ShowClearButton="true"
                                    FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                    Placeholder="Select Court"
                                    FloatLabelType="FloatLabelType.Always">
                        <DropDownListFieldSettings Text="Name" Value="Id" />
                    </SfDropDownList>
                    <ValidationMessage For="@(() => CaseDto.CourtId)" />
                </div>

                <div class="col-md-4">
                    <SfNumericTextBox TValue="int?" @bind-Value="CaseDto.CaseFilingYear"
                                      Placeholder="Filing Year"
                                      FloatLabelType="FloatLabelType.Always">
                    </SfNumericTextBox>
                </div>

                <div class="col-md-4">
                    <SfDropDownList TValue="int?" TItem="CaseCategoryDto"
                                    @bind-Value="CaseDto.CaseCategoryId"
                                    DataSource="@Categories"
                                    Placeholder="Select Category"
                                    AllowFiltering="true"
                                    ShowClearButton="true"
                                    FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                    FloatLabelType="FloatLabelType.Always">
                        <DropDownListFieldSettings Text="Title" Value="Id" />
                    </SfDropDownList>
                    <ValidationMessage For="@(() => CaseDto.CaseCategoryId)" />
                </div>

                <div class="col-md-4">
                    <SfDatePicker TValue="DateTime?" @bind-Value="CaseDto.DateInOffice"
                                  Placeholder="Date in Office"
                                  FloatLabelType="FloatLabelType.Always">
                    </SfDatePicker>
                </div>

                <div class="col-md-4">
                    <SfNumericTextBox TValue="decimal?" @bind-Value="CaseDto.ClaimAmount"
                                      Format="c2"
                                      Placeholder="Claim Amount"
                                      FloatLabelType="FloatLabelType.Always">
                    </SfNumericTextBox>
                </div>

                <div class="col-md-4">
                    <SfDropDownList TValue="int?" TItem="CaseNatureDto"
                                    @bind-Value="CaseDto.CaseNatureId"
                                    DataSource="@CaseNatures"
                                    Placeholder="Select Case Nature"
                                    FloatLabelType="FloatLabelType.Always"
                                    AllowFiltering="true"
                                    ShowClearButton="true"
                                    FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                        <DropDownListFieldSettings Text="Name" Value="Id" />
                    </SfDropDownList>
                </div>
                <div class="col-md-">
                    <div>Decided</div>
                    <SfSwitch OnLabel="Yes" OffLabel="No" @bind-Checked="CaseDto.IsDecided"></SfSwitch>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-body">
            <h5>Case Pray</h5>
            
            <Syncfusion.Blazor.RichTextEditor.SfRichTextEditor @bind-Value="CaseDto.Pray" >

            </Syncfusion.Blazor.RichTextEditor.SfRichTextEditor>
            
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-body">
            <h5>Case Synopsis</h5>
            <Syncfusion.Blazor.RichTextEditor.SfRichTextEditor @bind-Value="CaseDto.CaseSynopsis">

            </Syncfusion.Blazor.RichTextEditor.SfRichTextEditor>
        </div>
    </div>


    <!-- Plaintiffs Section -->
    <div class="card mb-3">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>Plaintiffs</h5>
                <SfButton type="button" OnClick="OpenPlaintiffForm" CssClass="e-primary">Add Plaintiff</SfButton>
            </div>
            <ValidationMessage For="@(() => CaseDto.Plaintiffs)" />

            @if (CaseDto.Plaintiffs?.Any() == true)
            {
                <SfGrid DataSource="CaseDto.Plaintiffs" AllowPaging="true" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn Field="@nameof(PlaintiffDto.PlaintiffType)" HeaderText="Type" AutoFit="true"></GridColumn>
                        <GridColumn HeaderText="Plaintiff" Width="250px">
                            <Template Context="gridContext">
                                @{
                                    if (gridContext is PlaintiffDto plaintiff)
                                    {
                                        @if (plaintiff.PlaintiffTypeId == 1)
                                        {
                                            @plaintiff.EmployeeName
                                        }
                                        else if (plaintiff.PlaintiffTypeId == 2)
                                        {
                                            @plaintiff.CouncilOfGroupCompanyName
                                        }
                                        else if (plaintiff.PlaintiffTypeId == 3)
                                        {
                                            @plaintiff.CouncilOfNonGroupCompanyName
                                        }
                                        else if (plaintiff.PlaintiffTypeId == 4)
                                        {
                                            @plaintiff.OtherPlaintiff
                                        }
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Field="@nameof(PlaintiffDto.LawFirmName)" HeaderText="Law Firm" Width="250px"></GridColumn>
                        <GridColumn Field="@nameof(PlaintiffDto.LawyerName)" HeaderText="Lawyer" Width="250px"></GridColumn>
                        <GridColumn HeaderText="Actions" AutoFit="true">
                            <Template Context="gridContext">
                                @{
                                    if (gridContext is PlaintiffDto plaintiff)
                                    {
                                        <SfButton OnClick="@(() => EditPlaintiff(plaintiff))"
                                                  type="button"
                                                  CssClass="e-primary e-small">Edit</SfButton>
                                        <SfButton OnClick="@(() => RemovePlaintiff(plaintiff.Id))"
                                                  type="button"
                                                  CssClass="e-danger e-small">Remove</SfButton>
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </div>
    </div>

    <!-- Respondents Section -->
    <div class="card mb-3">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>Respondents</h5>
                <SfButton type="button" OnClick="@(() => { _selectedRespondent = new RespondentDto(); _isRespondentDialogOpen = true; })" CssClass="e-primary">Add Respondent</SfButton>
            </div>

            @if (CaseDto.Respondents?.Any() == true)
            {
                <SfGrid DataSource="CaseDto.Respondents" AllowPaging="true" AllowSorting="true" AllowTextWrap="true"
                        AllowFiltering="true">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn Field="@nameof(RespondentDto.Name)" HeaderText="Name" Width="250px"></GridColumn>
                        <GridColumn Field="@nameof(RespondentDto.LawFirmName)" HeaderText="Law Firm" Width="250px"></GridColumn>
                        <GridColumn Field="@nameof(RespondentDto.LawyerName)" HeaderText="Lawyer" Width="250px"></GridColumn>
                        <GridColumn HeaderText="Actions" AutoFit="true">
                            <Template Context="gridContext">
                                @{
                                    if (gridContext is RespondentDto respondent)
                                    {
                                        <SfButton OnClick="@(() => EditRespondent(respondent))"
                                                  type="button"
                                                  CssClass="e-primary e-small">Edit</SfButton>
                                        <SfButton OnClick="@(() => RemoveRespondent(respondent))"
                                                  type="button"
                                                  CssClass="e-danger e-small">Remove</SfButton>
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </div>
    </div>

    <div class="d-flex gap-2 justify-content-end">
        <SfButton Type="submit" CssClass="e-primary">Save Case</SfButton>
        <SfButton OnClick="@(() => NavMgr.NavigateTo("/court-cases", true))" type="button"
                  CssClass="e-outline">Back to Cases</SfButton>
    </div>
</EditForm>


<!-- Verdict Selector Dialog -->
<SfDialog @bind-Visible="_isVerdictSelectorOpen" Width="800px" IsModal="true">
    <DialogTemplates>
        <Header>Select Verdict</Header>
        <Content>
            <SfGrid DataSource="Verdicts" AllowPaging="true">
                <GridColumns>
                    <GridColumn Field="@nameof(CaseVerdictDto.Title)" HeaderText="Title"></GridColumn>
                    <GridColumn Field="@nameof(CaseVerdictDto.Description)" HeaderText="Description"></GridColumn>
                    <GridColumn HeaderText="Actions" Width="100">
                        <Template>
                            @{
                                if (context is CaseVerdictDto verdict)
                                {
                                    <SfButton OnClick="@(() => SelectVerdict(verdict))"
                                              type="button"
                                              CssClass="e-primary">Select</SfButton>
                                }
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Plaintiff Form Dialog -->
<SfDialog @bind-Visible="_isPlaintiffDialogOpen" Width="600px" IsModal="true">
    <DialogTemplates>
        <Header>Plaintiff Detail</Header>
        <Content>
            <EditForm Model="_selectedPlaintiff" OnValidSubmit="SavePlaintiff">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="mb-3">
                    <SfDropDownList TItem="PlaintiffTypeDto" TValue="int"
                                    @bind-Value="_selectedPlaintiff.PlaintiffTypeId"
                                    DataSource="@_plaintiffTypes"
                                    Placeholder="Select Plaintiff Type"
                                    FloatLabelType="FloatLabelType.Always"
                                    AllowFiltering="true"
                                    FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                        <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                        <DropDownListEvents TValue="int" TItem="PlaintiffTypeDto"
                                            ValueChange="@(e => OnPlaintiffTypeChanged(e))">
                        </DropDownListEvents>
                    </SfDropDownList>
                </div>

                @if (_selectedPlaintiff.PlaintiffTypeId == 1)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="CaseEmployeeDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.CaseEmployeeId"
                                        DataSource="@_employees"
                                        Placeholder="Select Employee"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="Name" Value="Id"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="CaseEmployeeDto"
                                                ValueChange="@(e => OnEmployeeSelectionChanged(e))">
                            </DropDownListEvents>
                        </SfDropDownList>
                    </div>

                    <div class="mb-3">
                        <SfTextBox @bind-Value="_selectedPlaintiff.EmployeeName"
                                   Placeholder="Employee Name"
                                   Enabled="false"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>

                    <div class="mb-3">
                        <SfTextBox @bind-Value="_selectedPlaintiff.EmployeeCode"
                                   Placeholder="Employee Code"
                                   Enabled="false"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId == 2)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="CouncilOfGroupCompanyDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.CouncilOfGroupCompanyId"
                                        DataSource="@_groupCompanyCouncils"
                                        Placeholder="Select Group Company Council"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId == 3)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="CouncilOfNonGroupCompanyDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.CouncilOfNonGroupCompanyId"
                                        DataSource="@_nonGroupCompanyCouncils"
                                        Placeholder="Select Non-Group Company Council"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId == 4)
                {
                    <div class="mb-3">
                        <SfTextBox @bind-Value="_selectedPlaintiff.OtherPlaintiff"
                                   Placeholder="Other Plaintiff"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId != 4)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="LawFirmDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.LawFirmId"
                                        DataSource="@_lawFirms"
                                        Placeholder="Select Law Firm"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="LawFirmDto"
                                                ValueChange="@(e => OnLawFirmChanged(e))">
                            </DropDownListEvents>
                        </SfDropDownList>
                    </div>

                    <div class="mb-3">
                        <SfDropDownList TItem="LawyerDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.LawyerId"
                                        DataSource="@FilteredPlaintiffLawyers"
                                        Placeholder="Select Lawyer"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        Enabled="@(_selectedPlaintiff.LawFirmId.HasValue)"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <DropDownListFieldSettings Text="Name" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                }


                <div class="d-flex gap-2 justify-content-end">
                    <SfButton Type="submit" CssClass="e-primary">Save</SfButton>
                    <SfButton OnClick="@(() => _isPlaintiffDialogOpen = false)" type="button"
                              CssClass="e-outline">Cancel</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Respondent Form Dialog -->
<SfDialog Width="800px" IsModal="true" ShowCloseIcon="true" @bind-Visible="_isRespondentDialogOpen">
    <DialogTemplates>
        <Header>Respondent Detail</Header>
        <Content>
            <EditForm Model="_selectedRespondent" OnValidSubmit="SaveRespondent">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <SfTextBox @bind-Value="_selectedRespondent.Name"
                                   Placeholder="Name"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                        <ValidationMessage For="@(() => _selectedRespondent.Name)" />
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TValue="int?" TItem="LawFirmDto"
                                        @bind-Value="_selectedRespondent.LawFirmId"
                                        DataSource="@LawFirms"
                                        Placeholder="Select Law Firm"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Text="Title" Value="Id" />
                            @*<DropDownListEvents TValue="int?" TItem="LawFirmDto"
                                                 ValueChange="@OnLawFirmChange">
                                </DropDownListEvents>*@
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TValue="int?" TItem="LawyerDto"
                                        @bind-Value="_selectedRespondent.LawyerId"
                                        DataSource="@FilteredLawyers"
                                        Placeholder="Select Lawyer"
                                        FloatLabelType="FloatLabelType.Always"
                                        Enabled="@(_selectedRespondent.LawFirmId.HasValue)">
                            <DropDownListFieldSettings Text="Name" Value="Id" />
                        </SfDropDownList>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <SfButton OnClick="@(() => _isRespondentDialogOpen = false)" type="button"
                              CssClass="e-outline-secondary">Cancel</SfButton>
                    <SfButton Type="submit" CssClass="e-primary">Save</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code
{
    [Parameter] public int? Id { get; set; }
    //[Inject] private CourtCaseService CaseService { get; set; } = default!;
    [Inject] public LawFirmDataService LawFirmService { get; set; } = default!;
    private List<LawFirmDto> LawFirms { get; set; } = new();
    private SfToast? ToastObj;
    private List<LawyerDto> Lawyers { get; set; } = new();
    private List<LawyerDto> FilteredLawyers => _selectedRespondent.LawFirmId.HasValue
        ? Lawyers.Where(l => l.LawFirmId == _selectedRespondent.LawFirmId).ToList()
        : new List<LawyerDto>();

    private List<LawyerDto> FilteredPlaintiffLawyers => _selectedPlaintiff.LawFirmId.HasValue
        ? _lawyers.Where(l => l.LawFirmId == _selectedPlaintiff.LawFirmId).ToList()
        : new List<LawyerDto>();

    [Inject] private CourtDataService CourtService { get; set; } = default!;


    [Inject] private CaseVerdictService VerdictService { get; set; } = default!;
    [Inject] private CaseCategoryService CategoryService { get; set; } = default!;
    [Inject] private CaseEmployeeService EmployeeService { get; set; } = default!;
    //[Inject] private CouncilService CouncilService { get; set; } = default!;

    private CourtCaseDto? CaseDto { get; set; } = new() { Plaintiffs = new List<PlaintiffDto>(), Respondents = new List<RespondentDto>() };
    private List<CourtDto> Courts { get; set; } = new();
    private List<CaseCategoryDto> Categories { get; set; } = new();
    private List<CaseNatureDto> CaseNatures { get; set; } = new();

    [Inject] private CaseNatureService CaseNatureService { get; set; } = default!;

    private List<CaseVerdictDto> Verdicts { get; set; } = new();
    private List<CaseEmployeeDto> _employees = new();
    private List<CouncilOfGroupCompanyDto> _groupCompanyCouncils = new();
    private List<CouncilOfNonGroupCompanyDto> _nonGroupCompanyCouncils = new();
    private List<PlaintiffTypeDto> _plaintiffTypes = new();
    private List<LawFirmDto> _lawFirms = new();
    private List<LawyerDto> _lawyers = new();

    private bool _isPrayDialogOpen;
    private bool _isSynopsisSelectorOpen;
    private bool _isVerdictSelectorOpen;
    private bool _isPlaintiffDialogOpen;
    private bool _isRespondentDialogOpen;

    private PlaintiffDto _selectedPlaintiff = new();
    private RespondentDto _selectedRespondent = new();


    protected override async Task OnParametersSetAsync()
    {
        await LoadReferenceData();
        if (Id.HasValue)
        {
            await LoadCase();
        }
    }

    private async Task LoadReferenceData()
    {
        Courts = await CourtService.GetCourtsAsync();
        Categories = await CategoryService.GetCategoriesAsync();
        CaseNatures = await CaseNatureService.GetCaseNaturesAsync();

        Verdicts = await VerdictService.GetVerdictsAsync();
        _employees = await EmployeeService.GetCaseEmployeesAsync();
        _groupCompanyCouncils = await CaseService.GetGroupCompanyCouncilsAsync();
        _nonGroupCompanyCouncils = await CaseService.GetNonGroupCompanyCouncilsAsync();
        LawFirms = await LawFirmService.GetLawFirmsAsync();
        Lawyers = await LawFirmService.GetLawyersAsync();
        _plaintiffTypes = await PlaintiffTypeService.GetPlaintiffTypesAsync();
        _lawFirms = await LawFirmService.GetLawFirmsAsync();
        _lawyers = await LawFirmService.GetLawyersAsync();
    }

    private async Task LoadCase()
    {
        CaseDto = await CaseService.GetCourtCaseByIdAsync(Id.Value);

    }

    private async Task SaveCase()
    {
        var result = await CaseService.SaveCourtCaseAsync(CaseDto, "jawaid");
        if (result == "OK")
        {
            // display success message by using toastobj
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = "Case saved successfully",
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

        }
        else {
            // display error message
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
    }

    #region Pray Management
    private void OpenPraySelector() => _isPrayDialogOpen = true;




    #endregion

    #region Plaintiff Management
    private void OpenPlaintiffForm()
    {
        _selectedPlaintiff = new PlaintiffDto
        {
            CourtCaseId = Id ?? 0,
            Id = Guid.NewGuid() // Generate a new ID for the plaintiff
        };
        _isPlaintiffDialogOpen = true;
    }

    private void EditPlaintiff(PlaintiffDto plaintiff)
{
    _selectedPlaintiff = new PlaintiffDto
    {
        Id = plaintiff.Id,
        CourtCaseId = plaintiff.CourtCaseId,
        PlaintiffTypeId = plaintiff.PlaintiffTypeId,
        PlaintiffType = plaintiff.PlaintiffType,
        CaseEmployeeId = plaintiff.CaseEmployeeId,
        EmployeeCode = plaintiff.EmployeeCode,
        EmployeeName = plaintiff.EmployeeName,
        CNIC = plaintiff.CNIC,
        Department = plaintiff.Department,
        Designation = plaintiff.Designation,
        CouncilOfGroupCompanyId = plaintiff.CouncilOfGroupCompanyId,
        CouncilOfGroupCompanyName = plaintiff.CouncilOfGroupCompanyName,
        CouncilOfNonGroupCompanyId = plaintiff.CouncilOfNonGroupCompanyId,
        CouncilOfNonGroupCompanyName = plaintiff.CouncilOfNonGroupCompanyName,
        OtherPlaintiff = plaintiff.OtherPlaintiff,
        LawFirmId = plaintiff.LawFirmId,
        LawFirmName = plaintiff.LawFirmName,
        LawyerId = plaintiff.LawyerId,
        LawyerName = plaintiff.LawyerName
    };
    _isPlaintiffDialogOpen = true;
}

    private void SavePlaintiff()
    {
        // Set plaintiff type name
        var plaintiffType = _plaintiffTypes.FirstOrDefault(t => t.Id == _selectedPlaintiff.PlaintiffTypeId);
        if (plaintiffType != null)
        {
            _selectedPlaintiff.PlaintiffType = plaintiffType.Title;
        }

        // Set council names if applicable
        if (_selectedPlaintiff.CouncilOfGroupCompanyId.HasValue)
        {
            var groupCouncil = _groupCompanyCouncils.FirstOrDefault(c => c.Id == _selectedPlaintiff.CouncilOfGroupCompanyId);
            _selectedPlaintiff.CouncilOfGroupCompanyName = groupCouncil?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.CouncilOfGroupCompanyName = "";
        }

        if (_selectedPlaintiff.CouncilOfNonGroupCompanyId.HasValue)
        {
            var nonGroupCouncil = _nonGroupCompanyCouncils.FirstOrDefault(c => c.Id == _selectedPlaintiff.CouncilOfNonGroupCompanyId);
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = nonGroupCouncil?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = "";
        }

        // Set law firm and lawyer names if applicable
        if (_selectedPlaintiff.LawFirmId.HasValue)
        {
            var lawFirm = _lawFirms.FirstOrDefault(f => f.Id == _selectedPlaintiff.LawFirmId);
            _selectedPlaintiff.LawFirmName = lawFirm?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.LawFirmName = "";
        }

        if (_selectedPlaintiff.LawyerId.HasValue)
        {
            var lawyer = _lawyers.FirstOrDefault(l => l.Id == _selectedPlaintiff.LawyerId);
            _selectedPlaintiff.LawyerName = lawyer?.Name ?? "";
        }
        else
        {
            _selectedPlaintiff.LawyerName = "";
        }

        var existingPlaintiff = CaseDto.Plaintiffs.FirstOrDefault(p => p.Id == _selectedPlaintiff.Id);
        if (existingPlaintiff != null)
        {
            // Update existing plaintiff
            existingPlaintiff.PlaintiffTypeId = _selectedPlaintiff.PlaintiffTypeId;
            existingPlaintiff.PlaintiffType = _selectedPlaintiff.PlaintiffType;
            existingPlaintiff.CaseEmployeeId = _selectedPlaintiff.CaseEmployeeId;
            existingPlaintiff.EmployeeName = _selectedPlaintiff.EmployeeName;
            existingPlaintiff.EmployeeCode = _selectedPlaintiff.EmployeeCode;
            existingPlaintiff.CNIC = _selectedPlaintiff.CNIC;
            existingPlaintiff.Department = _selectedPlaintiff.Department;
            existingPlaintiff.Designation = _selectedPlaintiff.Designation;
            existingPlaintiff.CouncilOfGroupCompanyId = _selectedPlaintiff.CouncilOfGroupCompanyId;
            existingPlaintiff.CouncilOfGroupCompanyName = _selectedPlaintiff.CouncilOfGroupCompanyName;
            existingPlaintiff.CouncilOfNonGroupCompanyId = _selectedPlaintiff.CouncilOfNonGroupCompanyId;
            existingPlaintiff.CouncilOfNonGroupCompanyName = _selectedPlaintiff.CouncilOfNonGroupCompanyName;
            existingPlaintiff.OtherPlaintiff = _selectedPlaintiff.OtherPlaintiff;
            existingPlaintiff.LawFirmId = _selectedPlaintiff.LawFirmId;
            existingPlaintiff.LawFirmName = _selectedPlaintiff.LawFirmName;
            existingPlaintiff.LawyerId = _selectedPlaintiff.LawyerId;
            existingPlaintiff.LawyerName = _selectedPlaintiff.LawyerName;
        }
        else
        {
            // Add new plaintiff
            CaseDto.Plaintiffs.Add(_selectedPlaintiff);
        }

        // Sort plaintiffs by name or other appropriate field
        CaseDto.Plaintiffs = CaseDto.Plaintiffs.OrderBy(c =>
            c.PlaintiffTypeId == 1 ? c.EmployeeName :
            c.PlaintiffTypeId == 2 ? c.CouncilOfGroupCompanyName :
            c.PlaintiffTypeId == 3 ? c.CouncilOfNonGroupCompanyName :
            c.OtherPlaintiff).ToList();

        _isPlaintiffDialogOpen = false;
    }
    private void SavePlaintiff2()
    {
        // Set council names before saving
        if (_selectedPlaintiff.CouncilOfGroupCompanyId.HasValue)
        {
            var groupCouncil = _groupCompanyCouncils.FirstOrDefault(c => c.Id == _selectedPlaintiff.CouncilOfGroupCompanyId);
            _selectedPlaintiff.CouncilOfGroupCompanyName = groupCouncil?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.CouncilOfGroupCompanyName = "";
        }

        if (_selectedPlaintiff.CouncilOfNonGroupCompanyId.HasValue)
        {
            var nonGroupCouncil = _nonGroupCompanyCouncils.FirstOrDefault(c => c.Id == _selectedPlaintiff.CouncilOfNonGroupCompanyId);
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = nonGroupCouncil?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = "";
        }

        //if (_selectedPlaintiff.Id == Guid.Empty)  // Changed from 0 to Guid.Empty
        //{
        //    CaseDto.Plaintiffs.Add(_selectedPlaintiff);
        //}
        _isPlaintiffDialogOpen = false;
    }

    private void RemovePlaintiff(Guid Id)
    {
        //CaseDto.Plaintiffs.Remove(plaintiff);
        CaseDto.Plaintiffs = CaseDto.Plaintiffs.Where(c => c.Id != Id).ToList();

    }
    #endregion

    #region Synopsis Management
    private void OpenSynopsisSelector()
    {
        _isSynopsisSelectorOpen = true;
    }



    #endregion

    #region Verdict Management
    private void OpenVerdictSelector()
    {
        _isVerdictSelectorOpen = true;
    }

    private void RemoveVerdict()
    {
        CaseDto.CaseVerdictId = null;
        CaseDto.CaseVerdictTitle = "";
    }

    private void SelectVerdict(CaseVerdictDto verdict)
    {
        CaseDto.CaseVerdictId = verdict.Id;
        CaseDto.CaseVerdictTitle = verdict.Title;
        _isVerdictSelectorOpen = false;
    }
    #endregion

    private void OnPlaintiffTypeChanged(ChangeEventArgs<int, PlaintiffTypeDto> args)
    {
        _selectedPlaintiff.PlaintiffTypeId = args.Value;
        var selectedType = _plaintiffTypes.FirstOrDefault(t => t.Id == args.Value);
        if (selectedType != null)
        {
            _selectedPlaintiff.PlaintiffType = selectedType.Title;
        }

        // Reset fields based on plaintiff type
        if (_selectedPlaintiff.PlaintiffTypeId != 1)
        {
            _selectedPlaintiff.CaseEmployeeId = null;
            _selectedPlaintiff.EmployeeName = string.Empty;
            _selectedPlaintiff.EmployeeCode = string.Empty;
            _selectedPlaintiff.CNIC = string.Empty;
            _selectedPlaintiff.Department = string.Empty;
            _selectedPlaintiff.Designation = string.Empty;
        }

        if (_selectedPlaintiff.PlaintiffTypeId != 2)
        {
            _selectedPlaintiff.CouncilOfGroupCompanyId = null;
            _selectedPlaintiff.CouncilOfGroupCompanyName = string.Empty;
        }

        if (_selectedPlaintiff.PlaintiffTypeId != 3)
        {
            _selectedPlaintiff.CouncilOfNonGroupCompanyId = null;
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = string.Empty;
        }

        if (_selectedPlaintiff.PlaintiffTypeId != 4)
        {
            _selectedPlaintiff.OtherPlaintiff = string.Empty;
        }

        StateHasChanged();
    }

    private void OnLawFirmChanged(ChangeEventArgs<int?, LawFirmDto> args)
    {
        _selectedPlaintiff.LawFirmId = args.Value;
        _selectedPlaintiff.LawyerId = null;

        if (args.Value.HasValue)
        {
            var lawFirm = _lawFirms.FirstOrDefault(f => f.Id == args.Value);
            if (lawFirm != null)
            {
                _selectedPlaintiff.LawFirmName = lawFirm.Title;
            }
        }
        else
        {
            _selectedPlaintiff.LawFirmName = string.Empty;
        }

        StateHasChanged();
    }

    private void OnEmployeeSelectionChanged(ChangeEventArgs<int?, CaseEmployeeDto> args)
    {
        if (args.Value.HasValue)
        {
            var selectedEmployee = _employees.FirstOrDefault(e => e.Id == args.Value);
            if (selectedEmployee != null)
            {
                _selectedPlaintiff.EmployeeName = selectedEmployee.Name;
                _selectedPlaintiff.EmployeeCode = selectedEmployee.EmployeeCode;
                _selectedPlaintiff.CNIC = selectedEmployee.CNIC;
                _selectedPlaintiff.Department = selectedEmployee.Department;
                _selectedPlaintiff.Designation = selectedEmployee.Designation;
            }
        }
        else
        {
            _selectedPlaintiff.EmployeeName = string.Empty;
            _selectedPlaintiff.EmployeeCode = string.Empty;
            _selectedPlaintiff.CNIC = string.Empty;
            _selectedPlaintiff.Department = string.Empty;
            _selectedPlaintiff.Designation = string.Empty;
        }
        StateHasChanged();
    }

    //private async Task OnGroupCouncilChange(ChangeEventArgs<int?, CouncilOfGroupCompanyDto> args)
    //{
    //    if (args.Value.HasValue)
    //    {
    //        var council = _groupCompanyCouncils.FirstOrDefault(c => c.Id == args.Value);
    //        if (council != null)
    //        {
    //            _selectedPlaintiff.CouncilOfGroupCompanyName = council.Name;
    //        }
    //    }
    //    else
    //    {
    //        _selectedPlaintiff.CouncilOfGroupCompanyName = string.Empty;
    //    }
    //}

    //private async Task OnNonGroupCouncilChange(ChangeEventArgs<int?, CouncilOfNonGroupCompanyDto> args)
    //{
    //    if (args.Value.HasValue)
    //    {
    //        var council = _nonGroupCompanyCouncils.FirstOrDefault(c => c. == args.Value);
    //        if (council != null)
    //        {
    //            _selectedPlaintiff.CouncilOfNonGroupCompanyName = council.Name;
    //        }
    //    }
    //    else
    //    {
    //        _selectedPlaintiff.CouncilOfNonGroupCompanyName = string.Empty;
    //    }
    //}


    public void EditRespondent(RespondentDto resp)
    {
        _selectedRespondent = resp;
        _isRespondentDialogOpen = true;
    }

    public void RemoveRespondent(RespondentDto resp)
    {
        //CaseDto.Respondents?.Remove(resp);
        CaseDto.Respondents = CaseDto.Respondents.Where(c => c.Id != resp.Id).ToList();

    }

    public void SaveRespondent()
    {
        if (_selectedRespondent.LawFirmId.HasValue)
        {
            var lawFirm = LawFirms.First(f => f.Id == _selectedRespondent.LawFirmId);
            _selectedRespondent.LawFirmName = lawFirm.Title;
        }
        else
        {
            _selectedRespondent.LawFirmName = "";
        }

        if (_selectedRespondent.LawyerId.HasValue)
        {
            var lawyer = Lawyers.FirstOrDefault(l => l.Id == _selectedRespondent.LawyerId);
            if (lawyer == null)
                _selectedRespondent.LawyerName = "";
            else
            {
                _selectedRespondent.LawyerName = lawyer.Name;
            }

        }
        else
        {
            _selectedRespondent.LawyerName = "";
        }

        var rs = CaseDto.Respondents.FirstOrDefault(c => c.Id == _selectedRespondent.Id);
        if (rs != null)
        {
            rs.LawyerName = _selectedRespondent.LawyerName;
            rs.LawyerId = _selectedRespondent.LawyerId;
            rs.LawFirmId = _selectedRespondent.LawFirmId;
            rs.LawFirmName = _selectedRespondent.LawFirmName;
            rs.Name = _selectedRespondent.Name;
        }
        else
        {
            rs = new RespondentDto
            {
                LawFirmName = _selectedRespondent.LawFirmName,
                LawFirmId = _selectedRespondent.LawFirmId,
                Id = _selectedRespondent.Id,
                LawyerId = _selectedRespondent.LawyerId,
                LawyerName = _selectedRespondent.LawyerName,
                Name = _selectedRespondent.Name
            };

            CaseDto.Respondents.Add(rs);
            CaseDto.Respondents = CaseDto.Respondents.OrderBy(c => c.Name).ToList();

        }
        _isRespondentDialogOpen = false;
    }

    public void SaveRespondent2()
    {
        // Set the law firm name and lawyer name before saving
        if (_selectedRespondent.LawFirmId.HasValue)
        {
            var lawFirm = LawFirms.FirstOrDefault(f => f.Id == _selectedRespondent.LawFirmId);
            _selectedRespondent.LawFirmName = lawFirm?.Title ?? "";
        }
        else
        {
            _selectedRespondent.LawFirmName = "";
        }

        if (_selectedRespondent.LawyerId.HasValue)
        {
            var lawyer = Lawyers.FirstOrDefault(l => l.Id == _selectedRespondent.LawyerId);
            _selectedRespondent.LawyerName = lawyer?.Name ?? "";
        }
        else
        {
            _selectedRespondent.LawyerName = "";
        }

        if (CaseDto.Respondents == null)
        {
            CaseDto.Respondents = new List<RespondentDto>();
        }

        var existingRespondent = CaseDto.Respondents.FirstOrDefault(r => r.Id == _selectedRespondent.Id);
        if (existingRespondent != null)
        {
            // Update existing respondent
            var index = CaseDto.Respondents.IndexOf(existingRespondent);
            CaseDto.Respondents[index] = _selectedRespondent;
        }
        else
        {
            // Add new respondent
            CaseDto.Respondents.Add(_selectedRespondent);
        }

        _isRespondentDialogOpen = false;
    }

    //private void onlawfirmchange(int? lawfirmid)
    //{
    //    _selectedrespondent.lawfirmid = lawfirmid;
    //    _selectedrespondent.lawyerid = null; // reset lawyer selection when law firm changes
    //    statehaschanged();
    //}
}

